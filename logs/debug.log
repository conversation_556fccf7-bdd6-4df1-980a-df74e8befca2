[2m2025-08-04 15:43:40.640[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 15:43:41.082[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 85575 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-04 15:43:41.083[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 15:43:41.083[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 15:43:41.173[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 15:43:41.174[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 15:43:41.174[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 15:43:42.782[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.788[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.861[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 55 ms. Found 0 JPA repository interfaces.
[2m2025-08-04 15:43:42.873[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.891[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.909[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 16 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 15:43:42.926[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.931[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.962[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 15:43:44.772[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-04 15:43:44.823[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 15:43:44.830[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 15:43:44.830[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 15:43:45.010[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 15:43:45.011[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3836 ms
[2m2025-08-04 15:43:45.675[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@420dee82], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@930b6f9, com.mongodb.Jep395RecordCodecProvider@15363eba, com.mongodb.KotlinCodecProvider@6d6e8dfc]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@5c4c4e96], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 15:43:45.957[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=********, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=154033000, minRoundTripTimeNanos=0}
[2m2025-08-04 15:43:46.121[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 15:43:46.340[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@1fae279e'
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@30e787e2'
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@26be5ee'
[2m2025-08-04 15:43:46.734[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/ColorLabelConfigMapper.xml]'
[2m2025-08-04 15:43:46.812[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-04 15:43:46.869[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-04 15:43:46.916[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 15:43:46.951[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 15:43:46.988[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 15:43:47.015[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 15:43:47.048[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 15:43:47.075[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 15:43:47.107[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 15:43:47.134[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 15:43:47.156[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 15:43:47.178[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 15:43:47.213[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 15:43:47.224[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:6
[2m2025-08-04 15:43:47.772[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 15:43:47.978[0;39m [31mERROR[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 15:43:49.040[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-04 15:43:59.454[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-04 15:43:59.774[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 15:43:59.999[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 15:44:00.068[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 15:44:00.139[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 15:44:00.444[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 15:44:00.571[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 15:44:04.896[0;39m [33m WARN[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 15:44:05.294[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 15:43:46",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:726604599, ConnectTime:"2025-08-04 15:44:04", UseCount:1, LastActiveTime:"2025-08-04 15:44:05"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 15:44:06.826[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 15:44:08.350[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 15:44:09.625[0;39m [33m WARN[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 15:44:10.101[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 15:44:10.149[0;39m [33m WARN[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 7ccf6ea0-fe72-40f9-aacf-a8bfed6fbf03

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 15:44:10.164[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 15:44:10.731[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-04 15:44:11.162[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 7ccf6ea0-fe72-40f9-aacf-a8bfed6fbf03

[2m2025-08-04 15:44:11.387[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 15:44:11.454[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-04 15:44:11.473[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 31.626 seconds (process running for 35.338)
[2m2025-08-04 15:44:11.490[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 15:44:11.492[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 15:45:31.782[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 15:45:31.782[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 15:45:31.802[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 18 ms
[2m2025-08-04 15:45:36.290[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-04 15:45:36.363[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-04 15:45:36.370[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-04 15:45:36.554[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-04 15:45:36.611[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:45:36.776[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:36.799[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:45:36.801[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 15:45:36.973[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:40.763[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 15:45:40.784[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 15:45:40.787[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 15:45:40.790[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 15:45:40.792[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-04 15:45:40.858[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 15:45:40.879[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 15:45:40.882[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 15:45:41.076[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:45:41.078[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 15:45:41.078[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean), ACTIVE(String)
[2m2025-08-04 15:45:41.365[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:41.766[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 15:45:41.773[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 15:45:41.773[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 15:45:41.775[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 15:45:41.776[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 15:45:42.043[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:42.263[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 15:45:42.276[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 15:45:42.278[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 15:45:42.279[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 15:45:42.279[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-04 15:45:42.696[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 20
[2m2025-08-04 15:45:43.054[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT  
        ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
        POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS
     
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         ) 
        AND IS_ENABLED = 1
[2m2025-08-04 15:45:43.068[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT  
        ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
        POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS
     
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         ) 
        AND IS_ENABLED = 1
[2m2025-08-04 15:45:43.070[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT, POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS FROM COLOR_LABEL_CONFIG WHERE COIN_ID IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND IS_ENABLED = 1
[2m2025-08-04 15:45:43.070[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT, POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS FROM COLOR_LABEL_CONFIG WHERE COIN_ID IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND IS_ENABLED = 1
[2m2025-08-04 15:45:43.073[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m ==> Parameters: a3bad16c8ee5c51725c9c9e37d9c5896(String), d5b293b2b1ed15a8db7cfebd7b5717f4(String), a54814167b86225c761fc3f888704d03(String), 452da098c3a6c372742cbd86286cfaee(String), e333ee9813a7d4fbc236d9d179795365(String), 17b643e38f8bb0cd7b8c838a66eefd4c(String), 88d6677abdaef0e9f108d8def9b94c61(String), 03c1f240e4fea7a60fd718b436f37817(String), 1f71cf5ef33ea519e263a0e1b46935c0(String), 078cb94b47ccf7df4a456f6ca1361f1d(String), ae705f05b693b3b6a1d75d4e6dc2d5f2(String), 869713250f3a9e13cf1bc70ad32df682(String), a79d84551a7f93f7e518042f76d4f142(String), ddb195d502393e6a0cc20321914e6903(String), cf2fb963e0a2f146be425fe19b5f12d9(String), bb74c9dbe2617ef3322af0bda38b32fe(String), b35cc243b2163695f760358e4bfbaaa1(String), d361f4623a4068a3385ceb4142a31a42(String), b9170a55174ca081519b162338982c08(String), b043777d1d3a68ba469ff835e15792fd(String)
[2m2025-08-04 15:45:43.229[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 16:15:32.790[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:32.853[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:32.857[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:33.018[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:33.025[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 16:15:33.295[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:15:45.059[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 16:15:45.117[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
[2m2025-08-04 16:15:45.142[0;39m [33m WARN[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.payne.**.mapper]' package. Please check your configuration.
[2m2025-08-04 16:15:45.143[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 16:15:45.152[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 16:15:45.153[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 16:15:45.162[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 16:15:45.906[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 16:15:45.937[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 16:15:46.235[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:46.245[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:46.247[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:46.382[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:46.382[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 16:15:46.669[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:15:46.812[0;39m [33m WARN[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 16:15:47.541[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 15:43:46",
	ActiveCount:0,
	PoolingCount:2,
	CreateCount:2,
	DestroyCount:0,
	CloseCount:10,
	ConnectCount:10,
	Connections:[
		{ID:726604599, ConnectTime:"2025-08-04 15:44:04", UseCount:6, LastActiveTime:"2025-08-04 16:15:46"},
		{ID:670424084, ConnectTime:"2025-08-04 15:45:42", UseCount:4, LastActiveTime:"2025-08-04 16:15:47"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 16:15:47.888[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 16:15:57.411[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:57.432[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 16:15:57.439[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:57.441[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 16:15:57.445[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 16:15:57.723[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:58:15.513[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 16:58:15.815[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 99243 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-04 16:58:15.815[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 16:58:15.816[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 16:58:15.899[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 16:58:15.899[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 16:58:15.899[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 16:58:17.460[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:58:17.463[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 16:58:17.530[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 51 ms. Found 0 JPA repository interfaces.
[2m2025-08-04 16:58:17.539[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:58:17.539[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 16:58:17.557[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 16 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 16:58:17.568[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:58:17.569[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 16:58:17.591[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 16:58:18.792[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-04 16:58:18.818[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 16:58:18.820[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 16:58:18.821[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 16:58:18.952[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 16:58:18.953[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3053 ms
[2m2025-08-04 16:58:19.343[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@549561ab], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@2aa143ba, com.mongodb.Jep395RecordCodecProvider@7abe3d53, com.mongodb.KotlinCodecProvider@372dc92e]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@2af5eab6], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 16:58:19.577[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 16:58:19.792[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 16:58:19.799[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@726615d7'
[2m2025-08-04 16:58:19.803[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@f0c6326'
[2m2025-08-04 16:58:19.804[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@d8a5846'
[2m2025-08-04 16:58:20.008[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=********, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=*********, minRoundTripTimeNanos=0}
[2m2025-08-04 16:58:20.113[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/ColorLabelConfigMapper.xml]'
[2m2025-08-04 16:58:20.181[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-04 16:58:20.248[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-04 16:58:20.292[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 16:58:20.334[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 16:58:20.378[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 16:58:20.424[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 16:58:20.478[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 16:58:20.528[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 16:58:20.574[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 16:58:20.607[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 16:58:20.632[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 16:58:20.660[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 16:58:20.710[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 16:58:20.734[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:1
[2m2025-08-04 16:58:21.847[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 16:58:22.096[0;39m [31mERROR[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 16:58:23.805[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-04 16:58:31.528[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-04 16:58:32.347[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 16:58:33.216[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 16:58:33.326[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 16:58:33.433[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 16:58:33.916[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 16:58:34.088[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 16:58:36.290[0;39m [33m WARN[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 16:58:37.049[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 16:58:19",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1444201280, ConnectTime:"2025-08-04 16:58:35", UseCount:1, LastActiveTime:"2025-08-04 16:58:37"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 16:58:38.383[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 16:58:38.392[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 16:58:39.537[0;39m [33m WARN[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 16:58:40.030[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 16:58:40.079[0;39m [33m WARN[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: d229daeb-07a2-4ad8-bf05-f5a98f90e59f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 16:58:40.092[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 16:58:40.595[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-04 16:58:40.907[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: d229daeb-07a2-4ad8-bf05-f5a98f90e59f

[2m2025-08-04 16:58:41.058[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 16:58:41.106[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-04 16:58:41.121[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 26.318 seconds (process running for 30.647)
[2m2025-08-04 16:58:41.133[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 16:58:41.134[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 16:58:41.881[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[n(14)-127.0.0.1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 16:58:41.882[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[n(14)-127.0.0.1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 16:58:41.887[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[n(14)-127.0.0.1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 5 ms
[2m2025-08-04 17:00:31.049[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:31.049[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:31.214[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:31.214[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:31.221[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:31.221[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:31.374[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:31.454[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-04 17:00:32.110[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 17:00:32.115[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:32.117[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean), ACTIVE(String)
[2m2025-08-04 17:00:32.369[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:32.473[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:32.473[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:32.483[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:32.485[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:32.486[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:32.486[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:32.486[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:32.486[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-04 17:00:32.728[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 17:00:32.729[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:32.729[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean), ACTIVE(String)
[2m2025-08-04 17:00:32.967[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:33.003[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:33.009[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:33.010[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:33.011[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:33.011[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 17:00:33.252[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:34.623[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-04 17:00:34.636[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-04 17:00:34.639[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-04 17:00:34.655[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-04 17:00:34.656[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:34.771[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:34.794[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:00:34.795[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 17:00:34.960[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:37.695[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:37.695[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:37.715[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:37.715[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-04 17:00:37.718[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:37.718[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:37.724[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-04 17:00:37.725[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (IS_DEFAULT = ? AND STATUS = ?) ORDER BY CREATE_TIME DESC
[2m2025-08-04 17:00:37.727[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-04 17:00:37.727[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean), ACTIVE(String)
[2m2025-08-04 17:00:37.983[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:37.985[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 17:00:38.248[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:38.254[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:38.256[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:38.257[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:38.258[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: 8212430ca058b913b55f6bbc64fd8187(String)
[2m2025-08-04 17:00:38.518[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:38.827[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 17:00:38.837[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 17:00:38.840[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 17:00:38.841[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-04 17:00:38.841[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-04 17:00:39.220[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 20
[2m2025-08-04 17:00:39.530[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT  
        ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
        POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS
     
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         ) 
        AND IS_ENABLED = 1
[2m2025-08-04 17:00:39.542[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT  
        ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
        POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS
     
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         ) 
        AND IS_ENABLED = 1
[2m2025-08-04 17:00:39.544[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT, POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS FROM COLOR_LABEL_CONFIG WHERE COIN_ID IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND IS_ENABLED = 1
[2m2025-08-04 17:00:39.544[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT, POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS FROM COLOR_LABEL_CONFIG WHERE COIN_ID IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND IS_ENABLED = 1
[2m2025-08-04 17:00:39.545[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m ==> Parameters: a3bad16c8ee5c51725c9c9e37d9c5896(String), d5b293b2b1ed15a8db7cfebd7b5717f4(String), a54814167b86225c761fc3f888704d03(String), 452da098c3a6c372742cbd86286cfaee(String), e333ee9813a7d4fbc236d9d179795365(String), 17b643e38f8bb0cd7b8c838a66eefd4c(String), 88d6677abdaef0e9f108d8def9b94c61(String), 03c1f240e4fea7a60fd718b436f37817(String), 1f71cf5ef33ea519e263a0e1b46935c0(String), 078cb94b47ccf7df4a456f6ca1361f1d(String), ae705f05b693b3b6a1d75d4e6dc2d5f2(String), 869713250f3a9e13cf1bc70ad32df682(String), a79d84551a7f93f7e518042f76d4f142(String), ddb195d502393e6a0cc20321914e6903(String), cf2fb963e0a2f146be425fe19b5f12d9(String), bb74c9dbe2617ef3322af0bda38b32fe(String), b35cc243b2163695f760358e4bfbaaa1(String), d361f4623a4068a3385ceb4142a31a42(String), b9170a55174ca081519b162338982c08(String), b043777d1d3a68ba469ff835e15792fd(String)
[2m2025-08-04 17:00:39.699[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectByCoinIds             [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:50.467[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:50.480[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-04 17:00:50.483[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:50.484[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-04 17:00:50.487[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-04 17:00:51.392[0;39m [32mDEBUG[0;39m [35m99243[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:01:51.254[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-04 17:01:51.280[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-04 17:01:51.429[0;39m [32m INFO[0;39m [35m99243[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 17:03:13.304[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 17:03:14.057[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 1593 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-04 17:03:14.060[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 17:03:14.060[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 17:03:14.201[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 17:03:14.202[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 17:03:14.203[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 17:03:16.749[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:03:16.753[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 17:03:16.862[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 87 ms. Found 0 JPA repository interfaces.
[2m2025-08-04 17:03:16.875[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:03:16.875[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 17:03:16.895[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 17 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 17:03:16.908[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:03:16.910[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 17:03:16.987[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 17:03:18.612[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-04 17:03:18.646[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 17:03:18.648[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 17:03:18.648[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 17:03:18.719[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 17:03:18.720[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4516 ms
[2m2025-08-04 17:03:19.175[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@54e210e9], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@12c8b582, com.mongodb.Jep395RecordCodecProvider@748e220a, com.mongodb.KotlinCodecProvider@4463cc89]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@6950313f], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 17:03:19.485[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 17:03:19.494[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=********, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=188595792, minRoundTripTimeNanos=0}
[2m2025-08-04 17:03:19.703[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 17:03:19.708[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@58070eb3'
[2m2025-08-04 17:03:19.708[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1a67bf8'
[2m2025-08-04 17:03:19.711[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@2af7a0be'
[2m2025-08-04 17:03:20.055[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/ColorLabelConfigMapper.xml]'
[2m2025-08-04 17:03:20.117[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-04 17:03:20.175[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-04 17:03:20.212[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 17:03:20.239[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 17:03:20.276[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 17:03:20.304[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 17:03:20.337[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 17:03:20.359[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 17:03:20.382[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 17:03:20.407[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 17:03:20.433[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 17:03:20.455[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 17:03:20.490[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 17:03:20.503[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:14
[2m2025-08-04 17:03:20.775[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 17:03:20.895[0;39m [31mERROR[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 17:03:21.643[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-04 17:03:38.993[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[sson-netty-1-24][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-04 17:03:46.129[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[isson-netty-1-5][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-04 17:03:46.444[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 17:03:46.686[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 17:03:46.763[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 17:03:46.832[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 17:03:47.178[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 17:03:47.328[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 17:03:50.077[0;39m [33m WARN[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 17:03:50.490[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 17:03:19",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1531625651, ConnectTime:"2025-08-04 17:03:49", UseCount:1, LastActiveTime:"2025-08-04 17:03:50"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 17:03:52.070[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 17:03:52.084[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 17:03:53.231[0;39m [33m WARN[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 17:03:54.058[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 17:03:54.110[0;39m [33m WARN[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 711d0630-7674-4898-8dfe-c887474374d3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 17:03:54.125[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 17:03:54.651[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-04 17:03:54.995[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 711d0630-7674-4898-8dfe-c887474374d3

[2m2025-08-04 17:03:55.176[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 17:03:55.221[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-04 17:03:55.236[0;39m [32m INFO[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 45.342 seconds (process running for 52.415)
[2m2025-08-04 17:03:55.248[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 17:03:55.249[0;39m [32mDEBUG[0;39m [35m1593[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
