import { ref, computed } from 'vue';
import { fabric } from 'fabric/dist/fabric.min.js';
import { EleMessage } from 'ele-admin-plus/es';

/**
 * 生成唯一ID
 */
const generateId = () => {
  return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * 彩签元素管理Composable
 * <AUTHOR>
 * @date 2025-01-15
 */
export function useElementManager(canvas) {
  // 响应式数据
  const elements = ref([]);
  const selectedElement = ref(null);
  const clipboard = ref(null);

  // 元素类型定义
  const elementTypes = {
    TEXT: 'text',
    IMAGE: 'image',
    QRCODE: 'qrcode',
    RECTANGLE: 'rectangle',
    CIRCLE: 'circle',
    LINE: 'line'
  };

  // 默认样式配置
  const defaultStyles = {
    text: {
      fontSize: 14,
      fontFamily: 'Microsoft Yahei',
      fill: '#000000',
      fontWeight: 'normal',
      fontStyle: 'normal',
      textAlign: 'left',
      backgroundColor: 'transparent'
    },
    image: {
      opacity: 1,
      borderColor: 'transparent',
      borderWidth: 0
    },
    shape: {
      fill: '#ffffff',
      stroke: '#000000',
      strokeWidth: 1,
      opacity: 1
    }
  };

  // 计算属性
  const hasSelectedElement = computed(() => {
    return selectedElement.value !== null;
  });

  const canCopy = computed(() => {
    return hasSelectedElement.value;
  });

  const canPaste = computed(() => {
    return clipboard.value !== null;
  });

  /**
   * 添加文本元素
   * @param {Object} options - 文本选项
   */
  const addTextElement = (options = {}) => {
    if (!canvas.value) {
      EleMessage.error('画布未初始化');
      return;
    }

    const defaultOptions = {
      text: '双击编辑文本',
      left: 50,
      top: 50,
      ...defaultStyles.text,
      ...options
    };

    const textObject = new fabric.IText(defaultOptions.text, {
      ...defaultOptions,
      id: generateId(),
      elementType: elementTypes.TEXT,
      dataBinding: options.dataBinding || null
    });

    // 绑定文本编辑事件
    textObject.on('editing:entered', () => {
      console.log('开始编辑文本');
    });

    textObject.on('editing:exited', () => {
      console.log('结束编辑文本');
      updateElementsList();
    });

    canvas.value.add(textObject);
    canvas.value.setActiveObject(textObject);
    canvas.value.renderAll();

    updateElementsList();
    return textObject;
  };

  /**
   * 添加图片元素
   * @param {string} imageUrl - 图片URL
   * @param {Object} options - 图片选项
   */
  const addImageElement = (imageUrl, options = {}) => {
    if (!canvas.value) {
      EleMessage.error('画布未初始化');
      return;
    }

    if (!imageUrl) {
      EleMessage.error('图片URL不能为空');
      return;
    }

    fabric.Image.fromURL(imageUrl, (imgObject) => {
      const defaultOptions = {
        left: 50,
        top: 50,
        scaleX: 0.5,
        scaleY: 0.5,
        ...defaultStyles.image,
        ...options
      };

      imgObject.set({
        ...defaultOptions,
        id: generateId(),
        elementType: elementTypes.IMAGE
      });

      canvas.value.add(imgObject);
      canvas.value.setActiveObject(imgObject);
      canvas.value.renderAll();

      updateElementsList();
    }, {
      crossOrigin: 'anonymous'
    });
  };

  /**
   * 添加二维码元素
   * @param {Object} options - 二维码选项
   */
  const addQRCodeElement = (options = {}) => {
    if (!canvas.value) {
      EleMessage.error('画布未初始化');
      return;
    }

    const defaultOptions = {
      text: '{{diyCode}}',
      left: 50,
      top: 50,
      width: 60,
      height: 60,
      ...options
    };

    // 创建二维码占位符（实际二维码在预览时生成）
    const qrPlaceholder = new fabric.Rect({
      ...defaultOptions,
      fill: '#f0f0f0',
      stroke: '#cccccc',
      strokeWidth: 1,
      id: generateId(),
      elementType: elementTypes.QRCODE,
      dataBinding: defaultOptions.text
    });

    // 添加二维码标识文本
    const qrText = new fabric.Text('QR', {
      left: defaultOptions.left + defaultOptions.width / 2,
      top: defaultOptions.top + defaultOptions.height / 2,
      fontSize: 12,
      fill: '#666666',
      textAlign: 'center',
      originX: 'center',
      originY: 'center',
      selectable: false,
      evented: false
    });

    const qrGroup = new fabric.Group([qrPlaceholder, qrText], {
      left: defaultOptions.left,
      top: defaultOptions.top,
      id: generateId(),
      elementType: elementTypes.QRCODE,
      dataBinding: defaultOptions.text
    });

    canvas.value.add(qrGroup);
    canvas.value.setActiveObject(qrGroup);
    canvas.value.renderAll();

    updateElementsList();
    return qrGroup;
  };

  /**
   * 添加矩形元素
   * @param {Object} options - 矩形选项
   */
  const addRectangleElement = (options = {}) => {
    if (!canvas.value) {
      EleMessage.error('画布未初始化');
      return;
    }

    const defaultOptions = {
      left: 50,
      top: 50,
      width: 100,
      height: 60,
      ...defaultStyles.shape,
      ...options
    };

    const rectObject = new fabric.Rect({
      ...defaultOptions,
      id: generateId(),
      elementType: elementTypes.RECTANGLE
    });

    canvas.value.add(rectObject);
    canvas.value.setActiveObject(rectObject);
    canvas.value.renderAll();

    updateElementsList();
    return rectObject;
  };

  /**
   * 添加圆形元素
   * @param {Object} options - 圆形选项
   */
  const addCircleElement = (options = {}) => {
    if (!canvas.value) {
      EleMessage.error('画布未初始化');
      return;
    }

    const defaultOptions = {
      left: 50,
      top: 50,
      radius: 30,
      ...defaultStyles.shape,
      ...options
    };

    const circleObject = new fabric.Circle({
      ...defaultOptions,
      id: generateId(),
      elementType: elementTypes.CIRCLE
    });

    canvas.value.add(circleObject);
    canvas.value.setActiveObject(circleObject);
    canvas.value.renderAll();

    updateElementsList();
    return circleObject;
  };

  /**
   * 删除元素
   * @param {Object} element - 要删除的元素
   */
  const deleteElement = (element) => {
    if (!canvas.value || !element) return;

    canvas.value.remove(element);
    canvas.value.renderAll();
    updateElementsList();
  };

  /**
   * 复制元素
   * @param {Object} element - 要复制的元素
   */
  const copyElement = (element) => {
    if (!element) {
      element = canvas.value?.getActiveObject();
    }

    if (!element) {
      EleMessage.warning('请先选择要复制的元素');
      return;
    }

    element.clone((cloned) => {
      clipboard.value = cloned;
      EleMessage.success('元素已复制');
    });
  };

  /**
   * 粘贴元素
   */
  const pasteElement = () => {
    if (!canvas.value || !clipboard.value) {
      EleMessage.warning('剪贴板为空');
      return;
    }

    clipboard.value.clone((cloned) => {
      cloned.set({
        left: cloned.left + 10,
        top: cloned.top + 10,
        id: generateId()
      });

      canvas.value.add(cloned);
      canvas.value.setActiveObject(cloned);
      canvas.value.renderAll();
      updateElementsList();
    });
  };

  /**
   * 更新元素属性
   * @param {Object} element - 元素对象
   * @param {Object} properties - 要更新的属性
   */
  const updateElementProperties = (element, properties) => {
    if (!element || !properties) return;

    element.set(properties);
    canvas.value?.renderAll();
    updateElementsList();
  };

  /**
   * 设置元素层级
   * @param {Object} element - 元素对象
   * @param {string} action - 层级操作：'front', 'back', 'forward', 'backward'
   */
  const setElementLayer = (element, action) => {
    if (!canvas.value || !element) return;

    switch (action) {
      case 'front':
        canvas.value.bringToFront(element);
        break;
      case 'back':
        canvas.value.sendToBack(element);
        break;
      case 'forward':
        canvas.value.bringForward(element);
        break;
      case 'backward':
        canvas.value.sendBackwards(element);
        break;
    }

    canvas.value.renderAll();
    updateElementsList();
  };

  /**
   * 对齐元素
   * @param {string} alignment - 对齐方式
   */
  const alignElements = (alignment) => {
    if (!canvas.value) return;

    const activeObjects = canvas.value.getActiveObjects();
    if (activeObjects.length < 2) {
      EleMessage.warning('请选择至少两个元素进行对齐');
      return;
    }

    const canvasWidth = canvas.value.width;
    const canvasHeight = canvas.value.height;

    switch (alignment) {
      case 'left':
        const leftMost = Math.min(...activeObjects.map(obj => obj.left));
        activeObjects.forEach(obj => obj.set({ left: leftMost }));
        break;
      case 'right':
        const rightMost = Math.max(...activeObjects.map(obj => obj.left + obj.width * obj.scaleX));
        activeObjects.forEach(obj => obj.set({ left: rightMost - obj.width * obj.scaleX }));
        break;
      case 'top':
        const topMost = Math.min(...activeObjects.map(obj => obj.top));
        activeObjects.forEach(obj => obj.set({ top: topMost }));
        break;
      case 'bottom':
        const bottomMost = Math.max(...activeObjects.map(obj => obj.top + obj.height * obj.scaleY));
        activeObjects.forEach(obj => obj.set({ top: bottomMost - obj.height * obj.scaleY }));
        break;
      case 'centerH':
        const centerX = canvasWidth / 2;
        activeObjects.forEach(obj => obj.set({ left: centerX - (obj.width * obj.scaleX) / 2 }));
        break;
      case 'centerV':
        const centerY = canvasHeight / 2;
        activeObjects.forEach(obj => obj.set({ top: centerY - (obj.height * obj.scaleY) / 2 }));
        break;
    }

    canvas.value.renderAll();
  };

  /**
   * 更新元素列表
   */
  const updateElementsList = () => {
    if (!canvas.value) return;

    const objects = canvas.value.getObjects().filter(obj => !obj.isGrid);
    elements.value = objects.map(obj => ({
      id: obj.id,
      type: obj.elementType || 'unknown',
      name: getElementName(obj),
      visible: obj.visible !== false,
      locked: obj.selectable === false,
      object: obj
    }));
  };

  /**
   * 获取元素显示名称
   * @param {Object} obj - Fabric对象
   */
  const getElementName = (obj) => {
    switch (obj.elementType) {
      case elementTypes.TEXT:
        return `文本: ${obj.text?.substring(0, 10) || ''}`;
      case elementTypes.IMAGE:
        return '图片';
      case elementTypes.QRCODE:
        return '二维码';
      case elementTypes.RECTANGLE:
        return '矩形';
      case elementTypes.CIRCLE:
        return '圆形';
      case elementTypes.LINE:
        return '线条';
      default:
        return '未知元素';
    }
  };

  /**
   * 选择元素
   * @param {Object} element - 元素对象
   */
  const selectElement = (element) => {
    if (!canvas.value || !element) return;

    canvas.value.setActiveObject(element.object);
    canvas.value.renderAll();
    selectedElement.value = element;
  };

  /**
   * 切换元素可见性
   * @param {Object} element - 元素对象
   */
  const toggleElementVisibility = (element) => {
    if (!element || !element.object) return;

    const newVisibility = !element.visible;
    element.object.set({ visible: newVisibility });
    element.visible = newVisibility;
    canvas.value?.renderAll();
  };

  /**
   * 切换元素锁定状态
   * @param {Object} element - 元素对象
   */
  const toggleElementLock = (element) => {
    if (!element || !element.object) return;

    const newLockState = !element.locked;
    element.object.set({
      selectable: !newLockState,
      evented: !newLockState
    });
    element.locked = newLockState;
    canvas.value?.renderAll();
  };

  return {
    // 响应式数据
    elements,
    selectedElement,
    elementTypes,
    defaultStyles,

    // 计算属性
    hasSelectedElement,
    canCopy,
    canPaste,

    // 方法
    addTextElement,
    addImageElement,
    addQRCodeElement,
    addRectangleElement,
    addCircleElement,
    deleteElement,
    copyElement,
    pasteElement,
    updateElementProperties,
    setElementLayer,
    alignElements,
    updateElementsList,
    selectElement,
    toggleElementVisibility,
    toggleElementLock
  };
}
