<template>
  <div class="color-label-test">
    <h2>彩签功能测试页面</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>基础功能测试</span>
      </template>
      
      <div class="test-section">
        <h3>1. Fabric.js 导入测试</h3>
        <el-button @click="testFabricImport" type="primary">测试 Fabric.js 导入</el-button>
        <p class="test-result" :class="{ success: fabricImportSuccess, error: !fabricImportSuccess }">
          {{ fabricImportResult }}
        </p>
      </div>
      
      <div class="test-section">
        <h3>2. 工具函数测试</h3>
        <el-button @click="testUtilFunctions" type="primary">测试工具函数</el-button>
        <p class="test-result" :class="{ success: utilFunctionsSuccess, error: !utilFunctionsSuccess }">
          {{ utilFunctionsResult }}
        </p>
      </div>
      
      <div class="test-section">
        <h3>3. API 接口测试</h3>
        <el-button @click="testApiInterface" type="primary">测试 API 接口</el-button>
        <p class="test-result" :class="{ success: apiInterfaceSuccess, error: !apiInterfaceSuccess }">
          {{ apiInterfaceResult }}
        </p>
      </div>
      
      <div class="test-section">
        <h3>4. 画布初始化测试</h3>
        <el-button @click="testCanvasInit" type="primary">测试画布初始化</el-button>
        <div class="canvas-container">
          <canvas id="test-canvas" width="300" height="200" style="border: 1px solid #ccc;"></canvas>
        </div>
        <p class="test-result" :class="{ success: canvasInitSuccess, error: !canvasInitSuccess }">
          {{ canvasInitResult }}
        </p>
      </div>
    </el-card>
    
    <el-card class="test-card">
      <template #header>
        <span>组件测试</span>
      </template>
      
      <div class="test-section">
        <h3>5. 设计器组件测试</h3>
        <el-button @click="openDesigner" type="success">打开设计器</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  
  // 测试结果状态
  const fabricImportSuccess = ref(false);
  const fabricImportResult = ref('未测试');
  
  const utilFunctionsSuccess = ref(false);
  const utilFunctionsResult = ref('未测试');
  
  const apiInterfaceSuccess = ref(false);
  const apiInterfaceResult = ref('未测试');
  
  const canvasInitSuccess = ref(false);
  const canvasInitResult = ref('未测试');

  // 测试 Fabric.js 导入
  const testFabricImport = async () => {
    try {
      const { fabric } = await import('fabric/dist/fabric.min.js');
      if (fabric && fabric.Canvas) {
        fabricImportSuccess.value = true;
        fabricImportResult.value = 'Fabric.js 导入成功！版本: ' + (fabric.version || '未知');
      } else {
        throw new Error('Fabric.js 对象不完整');
      }
    } catch (error) {
      fabricImportSuccess.value = false;
      fabricImportResult.value = 'Fabric.js 导入失败: ' + error.message;
    }
  };

  // 测试工具函数
  const testUtilFunctions = async () => {
    try {
      const { generateId, processDataBinding, validateElement } = await import('../utils/index.js');
      
      // 测试 generateId
      const id = generateId();
      if (!id || typeof id !== 'string') {
        throw new Error('generateId 函数异常');
      }
      
      // 测试 processDataBinding
      const template = '钱币名称: {{coinName}}';
      const data = { coinName: '测试钱币' };
      const result = processDataBinding(template, data);
      if (result !== '钱币名称: 测试钱币') {
        throw new Error('processDataBinding 函数异常');
      }
      
      // 测试 validateElement
      const element = { type: 'text', left: 10, top: 20, text: '测试' };
      const validation = validateElement(element);
      if (!validation.valid) {
        throw new Error('validateElement 函数异常');
      }
      
      utilFunctionsSuccess.value = true;
      utilFunctionsResult.value = '工具函数测试通过！';
      
    } catch (error) {
      utilFunctionsSuccess.value = false;
      utilFunctionsResult.value = '工具函数测试失败: ' + error.message;
    }
  };

  // 测试 API 接口
  const testApiInterface = async () => {
    try {
      const { getAvailableDataFields } = await import('../api/index.js');
      
      if (typeof getAvailableDataFields !== 'function') {
        throw new Error('API 函数导入失败');
      }
      
      apiInterfaceSuccess.value = true;
      apiInterfaceResult.value = 'API 接口导入成功！';
      
    } catch (error) {
      apiInterfaceSuccess.value = false;
      apiInterfaceResult.value = 'API 接口测试失败: ' + error.message;
    }
  };

  // 测试画布初始化
  const testCanvasInit = async () => {
    try {
      const { fabric } = await import('fabric/dist/fabric.min.js');
      
      const canvas = new fabric.Canvas('test-canvas', {
        width: 300,
        height: 200,
        backgroundColor: '#f0f0f0'
      });
      
      // 添加一个测试文本
      const text = new fabric.Text('测试文本', {
        left: 50,
        top: 50,
        fontSize: 16,
        fill: '#333'
      });
      
      canvas.add(text);
      canvas.renderAll();
      
      canvasInitSuccess.value = true;
      canvasInitResult.value = '画布初始化成功！';
      
    } catch (error) {
      canvasInitSuccess.value = false;
      canvasInitResult.value = '画布初始化失败: ' + error.message;
    }
  };

  // 打开设计器
  const openDesigner = () => {
    EleMessage.info('设计器功能正在开发中...');
    // 这里可以跳转到设计器页面
    // this.$router.push('/bank-note/color-label/designer');
  };
</script>

<style scoped>
  .color-label-test {
    padding: 20px;
  }

  .test-card {
    margin-bottom: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 15px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .test-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
  }

  .test-result {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
  }

  .test-result.success {
    background-color: #f0f9ff;
    color: #1677ff;
    border: 1px solid #b3d8ff;
  }

  .test-result.error {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }

  .canvas-container {
    margin: 15px 0;
    text-align: center;
  }
</style>
