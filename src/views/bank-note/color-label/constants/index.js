/**
 * 彩签模板常量定义
 * <AUTHOR>
 * @date 2025-01-15
 */

// 元素类型常量
export const ELEMENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  QRCODE: 'qrcode',
  RECTANGLE: 'rectangle',
  CIRCLE: 'circle',
  LINE: 'line'
};

// 模板状态常量
export const TEMPLATE_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DRAFT: 'DRAFT'
};

// 模板类型常量
export const TEMPLATE_TYPES = {
  COLOR_LABEL: 'COLOR_LABEL',
  LOGO_TEMPLATE: 'LOGO_TEMPLATE',
  TEXT_TEMPLATE: 'TEXT_TEMPLATE'
};

// 数据字段分类常量
export const DATA_FIELD_CATEGORIES = {
  BASIC_INFO: 'BASIC_INFO',
  GRADE_INFO: 'GRADE_INFO',
  CUSTOMER_INFO: 'CUSTOMER_INFO',
  PHYSICAL_INFO: 'PHYSICAL_INFO'
};

// 对齐方式常量
export const ALIGNMENT_TYPES = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right',
  TOP: 'top',
  MIDDLE: 'middle',
  BOTTOM: 'bottom'
};

// 字体样式常量
export const FONT_STYLES = {
  NORMAL: 'normal',
  ITALIC: 'italic'
};

// 字体粗细常量
export const FONT_WEIGHTS = {
  NORMAL: 'normal',
  BOLD: 'bold'
};

// 图层操作常量
export const LAYER_ACTIONS = {
  BRING_TO_FRONT: 'front',
  SEND_TO_BACK: 'back',
  BRING_FORWARD: 'forward',
  SEND_BACKWARD: 'backward'
};

// 画布操作常量
export const CANVAS_ACTIONS = {
  ZOOM_IN: 'zoomIn',
  ZOOM_OUT: 'zoomOut',
  RESET_ZOOM: 'resetZoom',
  FIT_TO_SCREEN: 'fitToScreen'
};

// 文件类型常量
export const FILE_TYPES = {
  IMAGE: 'image',
  JSON: 'json',
  PDF: 'pdf'
};

// 导出格式常量
export const EXPORT_FORMATS = {
  PNG: 'png',
  JPEG: 'jpeg',
  PDF: 'pdf',
  JSON: 'json'
};

// 事件类型常量
export const EVENT_TYPES = {
  ELEMENT_ADDED: 'element:added',
  ELEMENT_MODIFIED: 'element:modified',
  ELEMENT_REMOVED: 'element:removed',
  SELECTION_CHANGED: 'selection:changed',
  CANVAS_READY: 'canvas:ready'
};

// 快捷键常量
export const SHORTCUTS = {
  SAVE: 'ctrl+s',
  PREVIEW: 'f5',
  UNDO: 'ctrl+z',
  REDO: 'ctrl+y',
  COPY: 'ctrl+c',
  PASTE: 'ctrl+v',
  DELETE: 'delete',
  SELECT_ALL: 'ctrl+a'
};

// 默认值常量
export const DEFAULTS = {
  CANVAS_WIDTH: 576,
  CANVAS_HEIGHT: 78,
  FONT_SIZE: 14,
  FONT_FAMILY: 'Microsoft Yahei',
  TEXT_COLOR: '#000000',
  BACKGROUND_COLOR: '#ffffff',
  BORDER_COLOR: '#000000',
  BORDER_WIDTH: 1,
  OPACITY: 1
};

// 限制常量
export const LIMITS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_TEMPLATE_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_CANVAS_WIDTH: 2000,
  MAX_CANVAS_HEIGHT: 2000,
  MIN_CANVAS_WIDTH: 100,
  MIN_CANVAS_HEIGHT: 50,
  MAX_FONT_SIZE: 72,
  MIN_FONT_SIZE: 8
};

// 错误消息常量
export const ERROR_MESSAGES = {
  CANVAS_NOT_READY: '画布未初始化',
  TEMPLATE_NAME_REQUIRED: '模板名称不能为空',
  TEMPLATE_NAME_EXISTS: '模板名称已存在',
  FILE_TOO_LARGE: '文件大小超过限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
  NETWORK_ERROR: '网络请求失败',
  SAVE_FAILED: '保存失败',
  LOAD_FAILED: '加载失败',
  DELETE_FAILED: '删除失败'
};

// 成功消息常量
export const SUCCESS_MESSAGES = {
  TEMPLATE_SAVED: '模板保存成功',
  TEMPLATE_DELETED: '模板删除成功',
  TEMPLATE_COPIED: '模板复制成功',
  FILE_UPLOADED: '文件上传成功',
  OPERATION_SUCCESS: '操作成功'
};

// API路径常量
export const API_PATHS = {
  TEMPLATES: '/color-label/templates',
  PREVIEW: '/color-label/preview',
  UPLOAD: '/color-label/upload-image',
  DATA_FIELDS: '/color-label/data-fields',
  VALIDATE: '/color-label/validate-config',
  EXPORT: '/color-label/export',
  IMPORT: '/color-label/import'
};

// 本地存储键常量
export const STORAGE_KEYS = {
  CURRENT_TEMPLATE: 'color_label_current_template',
  USER_PREFERENCES: 'color_label_user_preferences',
  RECENT_TEMPLATES: 'color_label_recent_templates',
  CANVAS_SETTINGS: 'color_label_canvas_settings'
};

// 颜色常量
export const COLORS = {
  PRIMARY: '#1677ff',
  SUCCESS: '#67c23a',
  WARNING: '#e6a23c',
  DANGER: '#f56c6c',
  INFO: '#909399',
  BLACK: '#000000',
  WHITE: '#ffffff',
  TRANSPARENT: 'transparent'
};

// 尺寸常量
export const SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

// 位置常量
export const POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom',
  LEFT: 'left',
  RIGHT: 'right',
  CENTER: 'center'
};
