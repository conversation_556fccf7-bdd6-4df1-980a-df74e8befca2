/**
 * 彩签模板类型定义
 * <AUTHOR>
 * @date 2025-01-15
 */

/**
 * 模板基础信息类型
 * @typedef {Object} TemplateInfo
 * @property {string} id - 模板ID
 * @property {string} templateName - 模板名称
 * @property {string} templateType - 模板类型
 * @property {string} status - 模板状态
 * @property {string} description - 模板描述
 * @property {boolean} isDefault - 是否为默认模板
 * @property {number} useCount - 使用次数
 * @property {string} createUser - 创建用户
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 画布配置类型
 * @typedef {Object} CanvasConfig
 * @property {number} width - 画布宽度
 * @property {number} height - 画布高度
 * @property {string} backgroundColor - 背景颜色
 * @property {number} actualWidth - 实际宽度（毫米）
 * @property {number} actualHeight - 实际高度（毫米）
 * @property {number} scale - 缩放比例
 * @property {GridConfig} gridConfig - 网格配置
 */

/**
 * 网格配置类型
 * @typedef {Object} GridConfig
 * @property {boolean} enabled - 是否启用网格
 * @property {number} size - 网格大小
 * @property {string} color - 网格颜色
 * @property {number} opacity - 网格透明度
 */

/**
 * 元素基础类型
 * @typedef {Object} BaseElement
 * @property {string} id - 元素ID
 * @property {string} type - 元素类型
 * @property {string} name - 元素名称
 * @property {number} left - X坐标
 * @property {number} top - Y坐标
 * @property {number} width - 宽度
 * @property {number} height - 高度
 * @property {number} angle - 旋转角度
 * @property {number} opacity - 透明度
 * @property {boolean} visible - 是否可见
 * @property {boolean} locked - 是否锁定
 * @property {string} dataBinding - 数据绑定字段
 * @property {number} zIndex - 层级索引
 */

/**
 * 文本元素类型
 * @typedef {BaseElement} TextElement
 * @property {'text'} type - 元素类型
 * @property {string} text - 文本内容
 * @property {number} fontSize - 字体大小
 * @property {string} fontFamily - 字体族
 * @property {string} fontWeight - 字体粗细
 * @property {string} fontStyle - 字体样式
 * @property {string} textAlign - 文本对齐
 * @property {string} fill - 文本颜色
 * @property {string} backgroundColor - 背景颜色
 * @property {boolean} underline - 是否下划线
 */

/**
 * 图片元素类型
 * @typedef {BaseElement} ImageElement
 * @property {'image'} type - 元素类型
 * @property {string} src - 图片源地址
 * @property {string} borderColor - 边框颜色
 * @property {number} borderWidth - 边框宽度
 */

/**
 * 二维码元素类型
 * @typedef {BaseElement} QRCodeElement
 * @property {'qrcode'} type - 元素类型
 * @property {string} data - 二维码数据
 * @property {string} errorCorrectionLevel - 纠错级别
 */

/**
 * 形状元素类型
 * @typedef {BaseElement} ShapeElement
 * @property {'rectangle'|'circle'} type - 元素类型
 * @property {string} fill - 填充颜色
 * @property {string} stroke - 边框颜色
 * @property {number} strokeWidth - 边框宽度
 */

/**
 * 数据字段类型
 * @typedef {Object} DataField
 * @property {string} key - 字段键
 * @property {string} label - 字段标签
 * @property {string} category - 字段分类
 * @property {string} description - 字段描述
 * @property {string} example - 示例值
 */

/**
 * 预设模板类型
 * @typedef {Object} PresetTemplate
 * @property {string} id - 模板ID
 * @property {string} name - 模板名称
 * @property {string} description - 模板描述
 * @property {string} thumbnail - 缩略图
 * @property {Array<BaseElement>} elements - 元素列表
 */

/**
 * 上传配置类型
 * @typedef {Object} UploadConfig
 * @property {number} maxSize - 最大文件大小
 * @property {Array<string>} allowedTypes - 允许的文件类型
 * @property {Array<string>} allowedExtensions - 允许的文件扩展名
 */

/**
 * 导出选项类型
 * @typedef {Object} ExportOptions
 * @property {string} format - 导出格式
 * @property {number} quality - 图片质量
 * @property {number} multiplier - 缩放倍数
 * @property {boolean} withBackground - 是否包含背景
 */

/**
 * 验证结果类型
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - 是否有效
 * @property {Array<string>} errors - 错误信息列表
 * @property {Array<string>} warnings - 警告信息列表
 */

/**
 * API响应类型
 * @typedef {Object} ApiResponse
 * @property {number} code - 响应码
 * @property {string} message - 响应消息
 * @property {*} data - 响应数据
 */

/**
 * 分页参数类型
 * @typedef {Object} PageParams
 * @property {number} current - 当前页码
 * @property {number} size - 页面大小
 * @property {string} templateName - 模板名称（搜索）
 * @property {string} templateType - 模板类型
 * @property {string} status - 模板状态
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 */

/**
 * 分页结果类型
 * @typedef {Object} PageResult
 * @property {Array<*>} records - 记录列表
 * @property {number} total - 总记录数
 * @property {number} current - 当前页码
 * @property {number} size - 页面大小
 * @property {number} pages - 总页数
 */

/**
 * 钱币数据类型
 * @typedef {Object} CoinData
 * @property {string} id - 钱币ID
 * @property {string} coinName - 钱币名称
 * @property {string} serialNumber - 钱币编号
 * @property {string} bankName - 银行名称
 * @property {string} gradeLevel - 品相等级
 * @property {string} specialMark - 特殊标记
 * @property {string} customerName - 客户名称
 * @property {string} diyCode - 送评条码
 */

/**
 * 预览数据类型
 * @typedef {Object} PreviewData
 * @property {TemplateInfo} template - 模板信息
 * @property {Array<CoinData>} coinData - 钱币数据
 * @property {Array<PreviewItem>} previewItems - 预览项列表
 */

/**
 * 预览项类型
 * @typedef {Object} PreviewItem
 * @property {string} coinId - 钱币ID
 * @property {CoinData} coinData - 钱币数据
 * @property {string} renderedHtml - 渲染后的HTML
 */

/**
 * 打印数据类型
 * @typedef {Object} PrintData
 * @property {TemplateInfo} colorTemplate - 彩签模板
 * @property {string} baseTemplateId - 基础模板ID
 * @property {Array<CoinData>} coinData - 钱币数据
 * @property {Array<PrintItem>} printItems - 打印项列表
 * @property {number} totalCount - 总数量
 */

/**
 * 打印项类型
 * @typedef {Object} PrintItem
 * @property {string} coinId - 钱币ID
 * @property {CoinData} coinData - 钱币数据
 * @property {string} composedHtml - 合成后的HTML
 */

// 导出类型定义（用于JSDoc）
export const Types = {
  TemplateInfo: 'TemplateInfo',
  CanvasConfig: 'CanvasConfig',
  GridConfig: 'GridConfig',
  BaseElement: 'BaseElement',
  TextElement: 'TextElement',
  ImageElement: 'ImageElement',
  QRCodeElement: 'QRCodeElement',
  ShapeElement: 'ShapeElement',
  DataField: 'DataField',
  PresetTemplate: 'PresetTemplate',
  UploadConfig: 'UploadConfig',
  ExportOptions: 'ExportOptions',
  ValidationResult: 'ValidationResult',
  ApiResponse: 'ApiResponse',
  PageParams: 'PageParams',
  PageResult: 'PageResult',
  CoinData: 'CoinData',
  PreviewData: 'PreviewData',
  PreviewItem: 'PreviewItem',
  PrintData: 'PrintData',
  PrintItem: 'PrintItem'
};
