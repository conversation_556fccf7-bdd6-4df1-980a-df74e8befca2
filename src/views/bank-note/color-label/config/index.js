/**
 * 彩签模板配置
 * <AUTHOR>
 * @date 2025-01-15
 */

// 画布默认配置
export const DEFAULT_CANVAS_CONFIG = {
  width: 576,        // 画布宽度（像素）
  height: 78,        // 画布高度（像素）
  backgroundColor: '#ffffff',
  actualWidth: 192,  // 实际宽度（毫米）
  actualHeight: 26,  // 实际高度（毫米）
  scale: 3,          // 缩放比例
  dpi: 300          // 分辨率
};

// 网格配置
export const DEFAULT_GRID_CONFIG = {
  enabled: true,
  size: 10,
  color: '#e0e0e0',
  opacity: 0.5
};

// 元素类型定义
export const ELEMENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  QRCODE: 'qrcode',
  RECTANGLE: 'rectangle',
  CIRCLE: 'circle',
  LINE: 'line'
};

// 元素默认样式
export const DEFAULT_ELEMENT_STYLES = {
  text: {
    fontSize: 14,
    fontFamily: 'Microsoft Yahei',
    fill: '#000000',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textAlign: 'left',
    backgroundColor: 'transparent'
  },
  image: {
    opacity: 1,
    borderColor: 'transparent',
    borderWidth: 0
  },
  shape: {
    fill: '#ffffff',
    stroke: '#000000',
    strokeWidth: 1,
    opacity: 1
  }
};

// 可用字体列表
export const AVAILABLE_FONTS = [
  { label: '微软雅黑', value: 'Microsoft Yahei' },
  { label: '宋体', value: 'SimSun' },
  { label: '黑体', value: 'SimHei' },
  { label: '楷体', value: 'KaiTi' },
  { label: 'Arial', value: 'Arial' },
  { label: 'Times New Roman', value: 'Times New Roman' },
  { label: 'Helvetica', value: 'Helvetica' },
  { label: 'Georgia', value: 'Georgia' }
];

// 字体大小选项
export const FONT_SIZE_OPTIONS = [
  8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 32, 36, 48, 72
];

// 数据字段配置
export const DATA_FIELDS = [
  {
    key: 'coinName',
    label: '钱币名称',
    category: 'BASIC_INFO',
    description: '钱币的完整名称',
    example: '中国人民银行1980年贰角'
  },
  {
    key: 'serialNumber',
    label: '钱币编号',
    category: 'BASIC_INFO',
    description: '钱币的序列号',
    example: 'S/N FZ-********'
  },
  {
    key: 'bankName',
    label: '银行名称',
    category: 'BASIC_INFO',
    description: '发行银行名称',
    example: '中国人民银行'
  },
  {
    key: 'yearInfo',
    label: '年份信息',
    category: 'BASIC_INFO',
    description: '钱币发行年份',
    example: '1980年'
  },
  {
    key: 'gradeLevel',
    label: '品相等级',
    category: 'GRADE_INFO',
    description: '钱币品相评级',
    example: '68 Superb Gem Unc'
  },
  {
    key: 'specialMark',
    label: '特殊标记',
    category: 'GRADE_INFO',
    description: '特殊评级标记',
    example: 'EPQ'
  },
  {
    key: 'customerName',
    label: '客户名称',
    category: 'CUSTOMER_INFO',
    description: '送评客户姓名',
    example: '张三'
  },
  {
    key: 'diyCode',
    label: '送评条码',
    category: 'CUSTOMER_INFO',
    description: '送评条形码',
    example: 'ZK25080001'
  }
];

// 数据字段分类
export const DATA_FIELD_CATEGORIES = {
  BASIC_INFO: '基础信息',
  GRADE_INFO: '评级信息',
  CUSTOMER_INFO: '客户信息',
  PHYSICAL_INFO: '物理属性'
};

// 模板状态
export const TEMPLATE_STATUS = {
  ACTIVE: { value: 'ACTIVE', label: '启用', color: 'success' },
  INACTIVE: { value: 'INACTIVE', label: '禁用', color: 'danger' },
  DRAFT: { value: 'DRAFT', label: '草稿', color: 'warning' }
};

// 模板类型
export const TEMPLATE_TYPES = {
  COLOR_LABEL: { value: 'COLOR_LABEL', label: '彩签模板', icon: 'el-icon-edit' },
  LOGO_TEMPLATE: { value: 'LOGO_TEMPLATE', label: 'Logo模板', icon: 'el-icon-picture' },
  TEXT_TEMPLATE: { value: 'TEXT_TEMPLATE', label: '文本模板', icon: 'el-icon-document' }
};

// 预设模板配置
export const PRESET_TEMPLATES = [
  {
    id: 'simple-text',
    name: '简单文本',
    description: '包含钱币名称和编号的简单文本布局',
    thumbnail: '/images/templates/simple-text.png',
    elements: [
      {
        type: 'text',
        text: '{{coinName}}',
        left: 20,
        top: 10,
        fontSize: 14,
        fontWeight: 'bold',
        dataBinding: 'coinName'
      },
      {
        type: 'text',
        text: '{{serialNumber}}',
        left: 20,
        top: 35,
        fontSize: 12,
        dataBinding: 'serialNumber'
      }
    ]
  },
  {
    id: 'with-qrcode',
    name: '带二维码',
    description: '包含文本信息和二维码的布局',
    thumbnail: '/images/templates/with-qrcode.png',
    elements: [
      {
        type: 'text',
        text: '{{coinName}}',
        left: 20,
        top: 10,
        fontSize: 12,
        dataBinding: 'coinName'
      },
      {
        type: 'qrcode',
        dataBinding: '{{diyCode}}',
        left: 120,
        top: 5,
        width: 50,
        height: 50
      }
    ]
  },
  {
    id: 'customer-info',
    name: '客户信息',
    description: '显示客户相关信息的布局',
    thumbnail: '/images/templates/customer-info.png',
    elements: [
      {
        type: 'text',
        text: '客户: {{customerName}}',
        left: 20,
        top: 10,
        fontSize: 12,
        dataBinding: 'customerName'
      },
      {
        type: 'text',
        text: '品相: {{gradeLevel}}',
        left: 20,
        top: 30,
        fontSize: 12,
        dataBinding: 'gradeLevel'
      }
    ]
  }
];

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
};

// 导出配置
export const EXPORT_CONFIG = {
  formats: [
    { value: 'png', label: 'PNG图片' },
    { value: 'jpeg', label: 'JPEG图片' },
    { value: 'pdf', label: 'PDF文档' },
    { value: 'json', label: 'JSON数据' }
  ],
  qualities: [
    { value: 0.5, label: '低质量' },
    { value: 0.8, label: '中等质量' },
    { value: 1.0, label: '高质量' }
  ]
};

// 快捷键配置
export const KEYBOARD_SHORTCUTS = {
  SAVE: { key: 'Ctrl+S', description: '保存模板' },
  PREVIEW: { key: 'F5', description: '预览模板' },
  UNDO: { key: 'Ctrl+Z', description: '撤销' },
  REDO: { key: 'Ctrl+Y', description: '重做' },
  COPY: { key: 'Ctrl+C', description: '复制' },
  PASTE: { key: 'Ctrl+V', description: '粘贴' },
  DELETE: { key: 'Delete', description: '删除' },
  SELECT_ALL: { key: 'Ctrl+A', description: '全选' }
};

// 颜色预设
export const COLOR_PRESETS = [
  '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
  '#ff0000', '#ff6600', '#ffcc00', '#66ff00', '#00ff66', '#00ffcc',
  '#0066ff', '#6600ff', '#cc00ff', '#ff0066', '#ff3366', '#ff6699'
];

// 验证规则
export const VALIDATION_RULES = {
  templateName: [
    { required: true, message: '模板名称不能为空', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在1到100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ]
};

// API接口配置
export const API_CONFIG = {
  baseURL: '/api/color-label',
  timeout: 30000,
  retryTimes: 3
};

// 缓存配置
export const CACHE_CONFIG = {
  templateListKey: 'color_label_template_list',
  currentTemplateKey: 'color_label_current_template',
  userPreferencesKey: 'color_label_user_preferences',
  expireTime: 30 * 60 * 1000 // 30分钟
};
