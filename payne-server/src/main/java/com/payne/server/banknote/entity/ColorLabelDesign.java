package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 彩签设计实体类
 * 基于现有的 LabelTemplate 扩展彩签设计功能
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Entity
@Table(name = "COLOR_LABEL_DESIGN")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("COLOR_LABEL_DESIGN")
public class ColorLabelDesign implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 设计名称
     */
    @Column(name = "DESIGN_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("DESIGN_NAME")
    private String designName;
    
    /**
     * 基础标签模板ID
     */
    @Column(name = "BASE_TEMPLATE_ID", columnDefinition = "VARCHAR2(50)")
    @TableField("BASE_TEMPLATE_ID")
    private String baseTemplateId;
    
    /**
     * 送评单号
     */
    @Column(name = "SENDFORM_NUMBER", columnDefinition = "VARCHAR2(50)")
    @TableField("SENDFORM_NUMBER")
    private String sendformNumber;
    
    /**
     * 关联的钱币ID列表（JSON格式）
     */
    @Column(name = "COIN_IDS", columnDefinition = "CLOB")
    @TableField("COIN_IDS")
    private String coinIds;
    
    /**
     * 彩签元素配置（JSON格式）
     * 存储在基础模板上添加的彩签元素
     */
    @Column(name = "COLOR_ELEMENTS", columnDefinition = "CLOB")
    @TableField("COLOR_ELEMENTS")
    private String colorElements;
    
    /**
     * 合成后的完整模板配置（JSON格式）
     * 基础模板 + 彩签元素的合成结果
     */
    @Column(name = "COMPOSED_TEMPLATE", columnDefinition = "CLOB")
    @TableField("COMPOSED_TEMPLATE")
    private String composedTemplate;
    
    /**
     * 设计类型：SINGLE-单个钱币，BATCH-批量钱币，TEMPLATE-设计模板
     */
    @Column(name = "DESIGN_TYPE", columnDefinition = "VARCHAR2(20)")
    @TableField("DESIGN_TYPE")
    private String designType;
    
    /**
     * 设计状态：DRAFT-草稿，SAVED-已保存，TEMPLATE-作为模板
     */
    @Column(name = "STATUS", columnDefinition = "VARCHAR2(10)")
    @TableField("STATUS")
    private String status;
    
    /**
     * 预览图片URL
     */
    @Column(name = "PREVIEW_IMAGE", columnDefinition = "VARCHAR2(500)")
    @TableField("PREVIEW_IMAGE")
    private String previewImage;
    
    /**
     * 使用次数
     */
    @Column(name = "USE_COUNT", columnDefinition = "NUMBER(10)")
    @TableField("USE_COUNT")
    private Integer useCount;
    
    /**
     * 最后使用时间
     */
    @Column(name = "LAST_USED_TIME")
    @TableField("LAST_USED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUsedTime;
    
    /**
     * 设计描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(500)")
    @TableField("DESCRIPTION")
    private String description;
    
    /**
     * 创建用户
     */
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新用户
     */
    @Column(name = "UPDATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("UPDATE_USER")
    private String updateUser;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 版本号（用于乐观锁）
     */
    @Column(name = "VERSION", columnDefinition = "NUMBER(10)")
    @TableField("VERSION")
    @Version
    private Integer version;
    
    /**
     * 逻辑删除标识：0-未删除，1-已删除
     */
    @Column(name = "DELETED", columnDefinition = "NUMBER(1)")
    @TableField("DELETED")
    @TableLogic
    private Integer deleted;
    
    /**
     * 扩展字段1
     */
    @Column(name = "EXT_FIELD1", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD1")
    private String extField1;
    
    /**
     * 扩展字段2
     */
    @Column(name = "EXT_FIELD2", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD2")
    private String extField2;
    
    /**
     * 扩展字段3（JSON格式）
     */
    @Column(name = "EXT_FIELD3", columnDefinition = "CLOB")
    @TableField("EXT_FIELD3")
    private String extField3;
}
