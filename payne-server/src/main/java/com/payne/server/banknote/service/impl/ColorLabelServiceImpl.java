package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.payne.server.banknote.entity.ColorLabelTemplate;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.mapper.ColorLabelTemplateMapper;
import com.payne.server.banknote.service.ColorLabelService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.vo.ColorLabelTemplateVO;
import com.payne.server.banknote.dto.ColorLabelTemplateDto;
import com.payne.server.common.core.web.PageParam;
import com.payne.server.common.core.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 彩签模板服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ColorLabelServiceImpl extends ServiceImpl<ColorLabelTemplateMapper, ColorLabelTemplate> 
        implements ColorLabelService {

    private final ObjectMapper objectMapper;
    private final PjOSendformItemService pjOSendformItemService;

    @Override
    public IPage<ColorLabelTemplateVO> pageTemplates(PageParam<ColorLabelTemplate, ?> page, 
                                                    QueryWrapper<ColorLabelTemplate> wrapper) {
        // 执行分页查询
        IPage<ColorLabelTemplate> entityPage = this.page(page, wrapper);
        
        // 转换为VO对象
        IPage<ColorLabelTemplateVO> voPage = new Page<>(
            entityPage.getCurrent(), 
            entityPage.getSize(), 
            entityPage.getTotal()
        );
        
        List<ColorLabelTemplateVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public ColorLabelTemplateVO getTemplateById(String id) {
        ColorLabelTemplate template = this.getById(id);
        return template != null ? convertToVO(template) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelTemplateVO saveTemplate(ColorLabelTemplateDto templateDto) {
        // 检查模板名称是否已存在
        if (isTemplateNameExists(templateDto.getTemplateName(), null)) {
            throw new RuntimeException("模板名称已存在");
        }

        ColorLabelTemplate template = new ColorLabelTemplate();
        BeanUtils.copyProperties(templateDto, template);
        
        // 处理JSON字段
        processJsonFields(templateDto, template);
        
        // 设置默认值
        template.setTemplateType(templateDto.getDefaultTemplateType());
        template.setStatus(templateDto.getDefaultStatus());
        template.setIsDefault(false);
        template.setUseCount(0);
        template.setCreateUser(SecurityUtil.getLoginUsername());
        template.setCreateTime(LocalDateTime.now());
        
        // 保存模板
        this.save(template);
        
        return convertToVO(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelTemplateVO updateTemplate(ColorLabelTemplateDto templateDto) {
        ColorLabelTemplate existingTemplate = this.getById(templateDto.getId());
        if (existingTemplate == null) {
            throw new RuntimeException("模板不存在");
        }

        // 检查模板名称是否已存在（排除当前模板）
        if (isTemplateNameExists(templateDto.getTemplateName(), templateDto.getId())) {
            throw new RuntimeException("模板名称已存在");
        }

        // 更新字段
        BeanUtils.copyProperties(templateDto, existingTemplate, "id", "createUser", "createTime", "useCount");
        
        // 处理JSON字段
        processJsonFields(templateDto, existingTemplate);
        
        // 设置更新信息
        existingTemplate.setUpdateUser(SecurityUtil.getLoginUsername());
        existingTemplate.setUpdateTime(LocalDateTime.now());
        
        // 更新模板
        this.updateById(existingTemplate);
        
        return convertToVO(existingTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(String id) {
        ColorLabelTemplate template = this.getById(id);
        if (template == null) {
            return false;
        }

        // 检查是否为默认模板
        if (Boolean.TRUE.equals(template.getIsDefault())) {
            throw new RuntimeException("默认模板不能删除");
        }

        // 检查是否有使用记录（可以根据业务需求决定是否允许删除）
        if (template.getUseCount() != null && template.getUseCount() > 0) {
            log.warn("删除已使用的模板: {}, 使用次数: {}", template.getTemplateName(), template.getUseCount());
        }

        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelTemplateVO copyTemplate(String sourceId, String newName) {
        ColorLabelTemplate sourceTemplate = this.getById(sourceId);
        if (sourceTemplate == null) {
            throw new RuntimeException("源模板不存在");
        }

        // 检查新模板名称是否已存在
        if (isTemplateNameExists(newName, null)) {
            throw new RuntimeException("模板名称已存在");
        }

        // 创建新模板
        ColorLabelTemplate newTemplate = new ColorLabelTemplate();
        BeanUtils.copyProperties(sourceTemplate, newTemplate, 
            "id", "templateName", "createUser", "createTime", "updateUser", "updateTime", 
            "useCount", "lastUsedTime", "isDefault");
        
        newTemplate.setTemplateName(newName);
        newTemplate.setIsDefault(false);
        newTemplate.setUseCount(0);
        newTemplate.setCreateUser(SecurityUtil.getLoginUsername());
        newTemplate.setCreateTime(LocalDateTime.now());
        
        // 保存新模板
        this.save(newTemplate);
        
        return convertToVO(newTemplate);
    }

    @Override
    public Map<String, Object> previewTemplate(String templateId, List<String> coinIds) {
        ColorLabelTemplate template = this.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        // 获取钱币数据
        List<PjOSendformItem> coins = pjOSendformItemService.listByIds(coinIds);
        if (coins.isEmpty()) {
            throw new RuntimeException("未找到钱币数据");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("template", convertToVO(template));
        result.put("coinData", coins);
        result.put("previewItems", generatePreviewItems(template, coins));
        
        return result;
    }

    @Override
    public Map<String, Object> generatePrintData(String templateId, List<String> coinIds, String baseTemplateId) {
        ColorLabelTemplate colorTemplate = this.getById(templateId);
        if (colorTemplate == null) {
            throw new RuntimeException("彩签模板不存在");
        }

        // 获取钱币数据
        List<PjOSendformItem> coins = pjOSendformItemService.listByIds(coinIds);
        if (coins.isEmpty()) {
            throw new RuntimeException("未找到钱币数据");
        }

        // 更新使用次数
        updateTemplateUsage(templateId);

        Map<String, Object> result = new HashMap<>();
        result.put("colorTemplate", convertToVO(colorTemplate));
        result.put("baseTemplateId", baseTemplateId);
        result.put("coinData", coins);
        result.put("printItems", generatePrintItems(colorTemplate, coins));
        result.put("totalCount", coins.size());
        
        return result;
    }

    @Override
    public Map<String, Object> uploadImage(MultipartFile file) {
        try {
            // 这里应该实现实际的文件上传逻辑
            // 可以上传到本地存储、OSS、CDN等
            
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename != null ? 
                originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
            
            String fileName = "color_label_" + System.currentTimeMillis() + fileExtension;
            
            // 模拟上传结果
            Map<String, Object> result = new HashMap<>();
            result.put("url", "/uploads/color-label/" + fileName);
            result.put("fileName", fileName);
            result.put("originalName", originalFilename);
            result.put("size", file.getSize());
            result.put("uploadTime", LocalDateTime.now());
            
            return result;
        } catch (Exception e) {
            log.error("上传图片失败", e);
            throw new RuntimeException("上传图片失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getAvailableDataFields() {
        List<Map<String, Object>> fields = new ArrayList<>();
        
        // 基础钱币信息字段
        addDataField(fields, "coinName", "钱币名称", "BASIC_INFO", "钱币的完整名称");
        addDataField(fields, "serialNumber", "钱币编号", "BASIC_INFO", "钱币的序列号");
        addDataField(fields, "bankName", "银行名称", "BASIC_INFO", "发行银行名称");
        addDataField(fields, "yearInfo", "年份信息", "BASIC_INFO", "钱币发行年份");
        addDataField(fields, "coinType", "钱币类型", "BASIC_INFO", "钱币的类型分类");
        
        // 评级信息字段
        addDataField(fields, "gradeLevel", "品相等级", "GRADE_INFO", "钱币品相评级");
        addDataField(fields, "gradeScore", "评级分数", "GRADE_INFO", "具体评级分数");
        addDataField(fields, "specialMark", "特殊标记", "GRADE_INFO", "特殊评级标记");
        addDataField(fields, "authenticity", "真伪结果", "GRADE_INFO", "真伪鉴定结果");
        
        // 客户信息字段
        addDataField(fields, "customerName", "客户名称", "CUSTOMER_INFO", "送评客户姓名");
        addDataField(fields, "diyCode", "送评条码", "CUSTOMER_INFO", "送评条形码");
        
        // 物理属性字段
        addDataField(fields, "weight", "重量", "PHYSICAL_INFO", "钱币重量");
        addDataField(fields, "size", "尺寸", "PHYSICAL_INFO", "钱币尺寸");
        
        return fields;
    }

    @Override
    public Map<String, Object> validateTemplateConfig(Map<String, Object> templateConfig) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        try {
            // 验证画布配置
            validateCanvasConfig(templateConfig, errors, warnings);
            
            // 验证元素配置
            validateElementsConfig(templateConfig, errors, warnings);
            
            result.put("valid", errors.isEmpty());
            result.put("errors", errors);
            result.put("warnings", warnings);
            
        } catch (Exception e) {
            log.error("验证模板配置失败", e);
            errors.add("验证过程中发生错误: " + e.getMessage());
            result.put("valid", false);
            result.put("errors", errors);
            result.put("warnings", warnings);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getTemplateStatistics(String templateId) {
        ColorLabelTemplate template = this.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("templateId", templateId);
        statistics.put("templateName", template.getTemplateName());
        statistics.put("useCount", template.getUseCount() != null ? template.getUseCount() : 0);
        statistics.put("lastUsedTime", template.getLastUsedTime());
        statistics.put("createTime", template.getCreateTime());
        statistics.put("updateTime", template.getUpdateTime());
        
        // 可以添加更多统计信息，如使用趋势、热门程度等
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteTemplates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        // 检查是否包含默认模板
        List<ColorLabelTemplate> templates = this.listByIds(ids);
        boolean hasDefaultTemplate = templates.stream()
                .anyMatch(t -> Boolean.TRUE.equals(t.getIsDefault()));
        
        if (hasDefaultTemplate) {
            throw new RuntimeException("不能删除默认模板");
        }

        return this.removeByIds(ids) ? ids.size() : 0;
    }

    @Override
    public Map<String, Object> exportTemplate(String id) {
        ColorLabelTemplate template = this.getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        Map<String, Object> exportData = new HashMap<>();
        exportData.put("template", convertToVO(template));
        exportData.put("exportTime", LocalDateTime.now());
        exportData.put("version", "1.0");
        
        return exportData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelTemplateVO importTemplate(MultipartFile file) {
        try {
            // 解析导入文件
            String content = new String(file.getBytes());
            Map<String, Object> importData = objectMapper.readValue(content, 
                new TypeReference<Map<String, Object>>() {});
            
            // 提取模板数据
            @SuppressWarnings("unchecked")
            Map<String, Object> templateData = (Map<String, Object>) importData.get("template");
            
            if (templateData == null) {
                throw new RuntimeException("导入文件格式错误");
            }

            // 创建新模板
            ColorLabelTemplate template = new ColorLabelTemplate();
            template.setTemplateName((String) templateData.get("templateName") + "_导入");
            template.setTemplateType((String) templateData.get("templateType"));
            template.setCanvasConfig((String) templateData.get("canvasConfig"));
            template.setCanvasData((String) templateData.get("canvasData"));
            template.setElements((String) templateData.get("elements"));
            template.setDescription((String) templateData.get("description"));
            template.setStatus("DRAFT");
            template.setIsDefault(false);
            template.setUseCount(0);
            template.setCreateUser(SecurityUtil.getLoginUsername());
            template.setCreateTime(LocalDateTime.now());
            
            // 保存模板
            this.save(template);
            
            return convertToVO(template);
            
        } catch (IOException e) {
            log.error("导入模板失败", e);
            throw new RuntimeException("导入模板失败: " + e.getMessage());
        }
    }

    @Override
    public List<ColorLabelTemplateVO> getTemplatesByType(String templateType) {
        QueryWrapper<ColorLabelTemplate> wrapper = new QueryWrapper<>();
        wrapper.eq("template_type", templateType)
               .eq("status", "ACTIVE")
               .orderByDesc("create_time");

        return this.list(wrapper).stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isTemplateNameExists(String templateName, String excludeId) {
        QueryWrapper<ColorLabelTemplate> wrapper = new QueryWrapper<>();
        wrapper.eq("template_name", templateName);

        if (StringUtils.hasText(excludeId)) {
            wrapper.ne("id", excludeId);
        }

        return this.count(wrapper) > 0;
    }

    @Override
    public ColorLabelTemplateVO getDefaultTemplate() {
        QueryWrapper<ColorLabelTemplate> wrapper = new QueryWrapper<>();
        wrapper.eq("is_default", true)
               .eq("status", "ACTIVE")
               .last("LIMIT 1");

        ColorLabelTemplate template = this.getOne(wrapper);
        return template != null ? convertToVO(template) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultTemplate(String templateId) {
        // 先清除所有默认模板标记
        this.update().set("is_default", false).update();

        // 设置新的默认模板
        ColorLabelTemplate template = this.getById(templateId);
        if (template == null) {
            return false;
        }

        template.setIsDefault(true);
        template.setUpdateUser(SecurityUtil.getLoginUsername());
        template.setUpdateTime(LocalDateTime.now());

        return this.updateById(template);
    }

    @Override
    public String getTemplatePreviewImage(String templateId) {
        ColorLabelTemplate template = this.getById(templateId);
        return template != null ? template.getPreviewImage() : null;
    }

    @Override
    public String generateTemplatePreviewImage(String templateId) {
        // 这里应该实现预览图生成逻辑
        // 可以使用Canvas API或其他图像处理库
        String previewUrl = "/api/color-label/templates/" + templateId + "/preview.png";

        // 更新模板的预览图URL
        ColorLabelTemplate template = this.getById(templateId);
        if (template != null) {
            template.setPreviewImage(previewUrl);
            template.setUpdateTime(LocalDateTime.now());
            this.updateById(template);
        }

        return previewUrl;
    }

    @Override
    public String composeTemplate(Map<String, Object> colorLabelData,
                                 Map<String, Object> baseTemplateData,
                                 Map<String, Object> coinData) {
        try {
            StringBuilder htmlBuilder = new StringBuilder();

            // 基础模板HTML
            String baseHtml = (String) baseTemplateData.get("html");
            if (baseHtml != null) {
                htmlBuilder.append(baseHtml);
            }

            // 添加彩签元素
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> elements = (List<Map<String, Object>>) colorLabelData.get("elements");

            if (elements != null) {
                for (Map<String, Object> element : elements) {
                    String elementHtml = renderElementToHtml(element, coinData);
                    htmlBuilder.append(elementHtml);
                }
            }

            return htmlBuilder.toString();

        } catch (Exception e) {
            log.error("合成模板失败", e);
            throw new RuntimeException("合成模板失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> convertCoordinates(Map<String, Object> designCoordinates,
                                                 Map<String, Object> canvasSize,
                                                 Map<String, Object> labelSize) {
        try {
            double designX = ((Number) designCoordinates.get("x")).doubleValue();
            double designY = ((Number) designCoordinates.get("y")).doubleValue();

            double canvasWidth = ((Number) canvasSize.get("width")).doubleValue();
            double canvasHeight = ((Number) canvasSize.get("height")).doubleValue();

            double labelWidth = ((Number) labelSize.get("width")).doubleValue();
            double labelHeight = ((Number) labelSize.get("height")).doubleValue();

            // 计算实际坐标
            double actualX = (designX / canvasWidth) * labelWidth;
            double actualY = (designY / canvasHeight) * labelHeight;

            Map<String, Object> result = new HashMap<>();
            result.put("x", actualX);
            result.put("y", actualY);

            return result;

        } catch (Exception e) {
            log.error("坐标转换失败", e);
            throw new RuntimeException("坐标转换失败: " + e.getMessage());
        }
    }

    @Override
    public String renderElementToHtml(Map<String, Object> element, Map<String, Object> coinData) {
        try {
            String elementType = (String) element.get("type");
            if (elementType == null) {
                return "";
            }

            StringBuilder htmlBuilder = new StringBuilder();

            switch (elementType) {
                case "text":
                    htmlBuilder.append(renderTextElement(element, coinData));
                    break;
                case "image":
                    htmlBuilder.append(renderImageElement(element, coinData));
                    break;
                case "qrcode":
                    htmlBuilder.append(renderQRCodeElement(element, coinData));
                    break;
                case "rectangle":
                case "circle":
                    htmlBuilder.append(renderShapeElement(element, coinData));
                    break;
                default:
                    log.warn("未知的元素类型: {}", elementType);
                    break;
            }

            return htmlBuilder.toString();

        } catch (Exception e) {
            log.error("渲染元素失败", e);
            return "";
        }
    }

    @Override
    public String processDataBinding(String template, Map<String, Object> data) {
        if (template == null || data == null) {
            return template;
        }

        String result = template;
        Pattern pattern = Pattern.compile("\\{\\{([^}]+)\\}\\}");
        Matcher matcher = pattern.matcher(template);

        while (matcher.find()) {
            String placeholder = matcher.group(0);
            String fieldName = matcher.group(1).trim();

            Object value = data.get(fieldName);
            String replacement = value != null ? value.toString() : "";

            result = result.replace(placeholder, replacement);
        }

        return result;
    }

    // 私有辅助方法
    private ColorLabelTemplateVO convertToVO(ColorLabelTemplate entity) {
        ColorLabelTemplateVO vo = new ColorLabelTemplateVO();
        BeanUtils.copyProperties(entity, vo);

        try {
            // 解析JSON字段
            if (StringUtils.hasText(entity.getCanvasConfig())) {
                vo.setCanvasConfig(objectMapper.readValue(entity.getCanvasConfig(),
                    new TypeReference<Map<String, Object>>() {}));
            }

            if (StringUtils.hasText(entity.getCanvasData())) {
                vo.setCanvasData(objectMapper.readValue(entity.getCanvasData(),
                    new TypeReference<Map<String, Object>>() {}));
            }

            if (StringUtils.hasText(entity.getElements())) {
                vo.setElements(objectMapper.readValue(entity.getElements(),
                    new TypeReference<List<Map<String, Object>>>() {}));
            }

        } catch (Exception e) {
            log.error("转换VO对象失败", e);
        }

        return vo;
    }

    private void processJsonFields(ColorLabelTemplateDto dto, ColorLabelTemplate entity) {
        try {
            // 处理画布配置
            if (dto.getCanvasConfigObj() != null) {
                entity.setCanvasConfig(objectMapper.writeValueAsString(dto.getCanvasConfigObj()));
            } else if (StringUtils.hasText(dto.getCanvasConfig())) {
                entity.setCanvasConfig(dto.getCanvasConfig());
            }

            // 处理画布数据
            if (dto.getCanvasDataObj() != null) {
                entity.setCanvasData(objectMapper.writeValueAsString(dto.getCanvasDataObj()));
            } else if (StringUtils.hasText(dto.getCanvasData())) {
                entity.setCanvasData(dto.getCanvasData());
            }

            // 处理元素列表
            if (dto.getElementsObj() != null) {
                entity.setElements(objectMapper.writeValueAsString(dto.getElementsObj()));
            } else if (StringUtils.hasText(dto.getElements())) {
                entity.setElements(dto.getElements());
            }

        } catch (Exception e) {
            log.error("处理JSON字段失败", e);
            throw new RuntimeException("处理JSON字段失败: " + e.getMessage());
        }
    }

    private void addDataField(List<Map<String, Object>> fields, String key, String label,
                             String category, String description) {
        Map<String, Object> field = new HashMap<>();
        field.put("key", key);
        field.put("label", label);
        field.put("category", category);
        field.put("description", description);
        fields.add(field);
    }

    private void validateCanvasConfig(Map<String, Object> templateConfig,
                                    List<String> errors, List<String> warnings) {
        @SuppressWarnings("unchecked")
        Map<String, Object> canvasConfig = (Map<String, Object>) templateConfig.get("canvasConfig");

        if (canvasConfig == null) {
            errors.add("画布配置不能为空");
            return;
        }

        // 验证画布尺寸
        Object width = canvasConfig.get("width");
        Object height = canvasConfig.get("height");

        if (width == null || height == null) {
            errors.add("画布尺寸不能为空");
        } else {
            try {
                int w = ((Number) width).intValue();
                int h = ((Number) height).intValue();

                if (w <= 0 || h <= 0) {
                    errors.add("画布尺寸必须大于0");
                }

                if (w > 2000 || h > 2000) {
                    warnings.add("画布尺寸过大，可能影响性能");
                }
            } catch (Exception e) {
                errors.add("画布尺寸格式错误");
            }
        }
    }

    private void validateElementsConfig(Map<String, Object> templateConfig,
                                      List<String> errors, List<String> warnings) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> elements = (List<Map<String, Object>>) templateConfig.get("elements");

        if (elements == null || elements.isEmpty()) {
            warnings.add("模板中没有任何元素");
            return;
        }

        for (int i = 0; i < elements.size(); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = (String) element.get("type");

            if (elementType == null) {
                errors.add("第" + (i + 1) + "个元素缺少类型定义");
                continue;
            }

            // 验证位置信息
            Object left = element.get("left");
            Object top = element.get("top");

            if (left == null || top == null) {
                errors.add("第" + (i + 1) + "个元素缺少位置信息");
            }
        }
    }

    private List<Map<String, Object>> generatePreviewItems(ColorLabelTemplate template,
                                                          List<PjOSendformItem> coins) {
        List<Map<String, Object>> previewItems = new ArrayList<>();

        for (PjOSendformItem coin : coins) {
            Map<String, Object> coinData = convertCoinToMap(coin);
            Map<String, Object> previewItem = new HashMap<>();
            previewItem.put("coinId", coin.getId());
            previewItem.put("coinData", coinData);
            previewItem.put("renderedHtml", renderTemplateForCoin(template, coinData));
            previewItems.add(previewItem);
        }

        return previewItems;
    }

    private List<Map<String, Object>> generatePrintItems(ColorLabelTemplate template,
                                                        List<PjOSendformItem> coins) {
        return generatePreviewItems(template, coins); // 目前预览和打印数据相同
    }

    private void updateTemplateUsage(String templateId) {
        ColorLabelTemplate template = this.getById(templateId);
        if (template != null) {
            template.setUseCount((template.getUseCount() != null ? template.getUseCount() : 0) + 1);
            template.setLastUsedTime(LocalDateTime.now());
            this.updateById(template);
        }
    }

    private Map<String, Object> convertCoinToMap(PjOSendformItem coin) {
        Map<String, Object> coinData = new HashMap<>();
        coinData.put("coinName", coin.getItemName());
        coinData.put("serialNumber", coin.getSerialNumber());
        coinData.put("bankName", coin.getBankName());
        coinData.put("gradeLevel", coin.getGradeLevel());
        coinData.put("specialMark", coin.getSpecialMark());
        coinData.put("customerName", coin.getCustomerName());
        coinData.put("diyCode", coin.getDiyCode());
        return coinData;
    }

    private String renderTemplateForCoin(ColorLabelTemplate template, Map<String, Object> coinData) {
        try {
            if (!StringUtils.hasText(template.getElements())) {
                return "";
            }

            List<Map<String, Object>> elements = objectMapper.readValue(template.getElements(),
                new TypeReference<List<Map<String, Object>>>() {});

            StringBuilder htmlBuilder = new StringBuilder();
            htmlBuilder.append("<div class=\"color-label-container\">");

            for (Map<String, Object> element : elements) {
                String elementHtml = renderElementToHtml(element, coinData);
                htmlBuilder.append(elementHtml);
            }

            htmlBuilder.append("</div>");
            return htmlBuilder.toString();

        } catch (Exception e) {
            log.error("渲染模板失败", e);
            return "";
        }
    }

    private String renderTextElement(Map<String, Object> element, Map<String, Object> coinData) {
        String text = (String) element.get("text");
        if (text == null) {
            text = "";
        }

        // 处理数据绑定
        text = processDataBinding(text, coinData);

        // 构建样式
        StringBuilder styleBuilder = new StringBuilder();
        styleBuilder.append("position: absolute;");

        if (element.get("left") != null) {
            styleBuilder.append("left: ").append(element.get("left")).append("px;");
        }
        if (element.get("top") != null) {
            styleBuilder.append("top: ").append(element.get("top")).append("px;");
        }
        if (element.get("fontSize") != null) {
            styleBuilder.append("font-size: ").append(element.get("fontSize")).append("px;");
        }
        if (element.get("color") != null) {
            styleBuilder.append("color: ").append(element.get("color")).append(";");
        }
        if (element.get("fontFamily") != null) {
            styleBuilder.append("font-family: ").append(element.get("fontFamily")).append(";");
        }

        return String.format("<div style=\"%s\">%s</div>", styleBuilder.toString(), text);
    }

    private String renderImageElement(Map<String, Object> element, Map<String, Object> coinData) {
        String src = (String) element.get("src");
        if (src == null) {
            return "";
        }

        StringBuilder styleBuilder = new StringBuilder();
        styleBuilder.append("position: absolute;");

        if (element.get("left") != null) {
            styleBuilder.append("left: ").append(element.get("left")).append("px;");
        }
        if (element.get("top") != null) {
            styleBuilder.append("top: ").append(element.get("top")).append("px;");
        }
        if (element.get("width") != null) {
            styleBuilder.append("width: ").append(element.get("width")).append("px;");
        }
        if (element.get("height") != null) {
            styleBuilder.append("height: ").append(element.get("height")).append("px;");
        }

        return String.format("<img src=\"%s\" style=\"%s\" />", src, styleBuilder.toString());
    }

    private String renderQRCodeElement(Map<String, Object> element, Map<String, Object> coinData) {
        String dataBinding = (String) element.get("dataBinding");
        String qrData = dataBinding != null ? processDataBinding(dataBinding, coinData) : "";

        StringBuilder styleBuilder = new StringBuilder();
        styleBuilder.append("position: absolute;");

        if (element.get("left") != null) {
            styleBuilder.append("left: ").append(element.get("left")).append("px;");
        }
        if (element.get("top") != null) {
            styleBuilder.append("top: ").append(element.get("top")).append("px;");
        }
        if (element.get("width") != null) {
            styleBuilder.append("width: ").append(element.get("width")).append("px;");
        }
        if (element.get("height") != null) {
            styleBuilder.append("height: ").append(element.get("height")).append("px;");
        }

        // 这里应该生成实际的二维码图片URL
        String qrCodeUrl = "/api/qrcode/generate?data=" + qrData;

        return String.format("<img src=\"%s\" style=\"%s\" />", qrCodeUrl, styleBuilder.toString());
    }

    private String renderShapeElement(Map<String, Object> element, Map<String, Object> coinData) {
        String elementType = (String) element.get("type");

        StringBuilder styleBuilder = new StringBuilder();
        styleBuilder.append("position: absolute;");

        if (element.get("left") != null) {
            styleBuilder.append("left: ").append(element.get("left")).append("px;");
        }
        if (element.get("top") != null) {
            styleBuilder.append("top: ").append(element.get("top")).append("px;");
        }
        if (element.get("width") != null) {
            styleBuilder.append("width: ").append(element.get("width")).append("px;");
        }
        if (element.get("height") != null) {
            styleBuilder.append("height: ").append(element.get("height")).append("px;");
        }
        if (element.get("backgroundColor") != null) {
            styleBuilder.append("background-color: ").append(element.get("backgroundColor")).append(";");
        }
        if (element.get("borderColor") != null) {
            styleBuilder.append("border-color: ").append(element.get("borderColor")).append(";");
        }
        if (element.get("borderWidth") != null) {
            styleBuilder.append("border-width: ").append(element.get("borderWidth")).append("px;");
            styleBuilder.append("border-style: solid;");
        }

        if ("circle".equals(elementType)) {
            styleBuilder.append("border-radius: 50%;");
        }

        return String.format("<div style=\"%s\"></div>", styleBuilder.toString());
    }
}
